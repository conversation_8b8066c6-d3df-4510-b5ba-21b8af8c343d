// 회귀 분석 유틸리티

import { RegressionResult } from './types';

/**
 * 단순 선형 회귀 분석 (OLS)
 */
export function simpleLinearRegression(x: number[], y: number[]): RegressionResult {
  if (x.length !== y.length || x.length < 2) {
    throw new Error('Invalid data for regression analysis');
  }

  const n = x.length;
  
  // 평균 계산
  const xMean = x.reduce((a, b) => a + b, 0) / n;
  const yMean = y.reduce((a, b) => a + b, 0) / n;
  
  // 베타 계산 (기울기)
  let numerator = 0;
  let denominator = 0;
  
  for (let i = 0; i < n; i++) {
    numerator += (x[i] - xMean) * (y[i] - yMean);
    denominator += Math.pow(x[i] - xMean, 2);
  }
  
  if (denominator === 0) {
    throw new Error('Cannot calculate regression: denominator is zero');
  }
  
  const beta = numerator / denominator;
  const alpha = yMean - beta * xMean;
  
  // R² 계산
  let ssRes = 0; // 잔차 제곱합
  let ssTot = 0; // 총 제곱합
  
  for (let i = 0; i < n; i++) {
    const predicted = alpha + beta * x[i];
    ssRes += Math.pow(y[i] - predicted, 2);
    ssTot += Math.pow(y[i] - yMean, 2);
  }
  
  const r2 = ssTot === 0 ? 1 : 1 - (ssRes / ssTot);
  
  // t-통계량 계산
  const mse = ssRes / (n - 2); // 평균 제곱 오차
  const seBeta = Math.sqrt(mse / denominator); // 베타의 표준오차
  const tstat = seBeta === 0 ? 0 : beta / seBeta;
  
  // p-value 계산 (근사치)
  const pvalue = calculatePValue(Math.abs(tstat), n - 2);
  
  return {
    beta,
    r2,
    tstat,
    pvalue
  };
}

/**
 * t-분포를 이용한 p-value 근사 계산
 */
function calculatePValue(tStat: number, df: number): number {
  // 간단한 t-분포 p-value 근사
  // 실제로는 더 정확한 계산이 필요하지만, 여기서는 근사치 사용
  
  if (df <= 0) return 1;
  if (tStat === 0) return 1;
  
  // 자유도가 큰 경우 정규분포로 근사
  if (df >= 30) {
    return 2 * (1 - normalCDF(Math.abs(tStat)));
  }
  
  // 간단한 근사 공식 (정확하지 않음)
  const x = tStat * tStat / (tStat * tStat + df);
  return 1 - Math.pow(1 - x, df / 2);
}

/**
 * 표준정규분포 누적분포함수 근사
 */
function normalCDF(x: number): number {
  // Abramowitz and Stegun 근사
  const a1 =  0.254829592;
  const a2 = -0.284496736;
  const a3 =  1.421413741;
  const a4 = -1.453152027;
  const a5 =  1.061405429;
  const p  =  0.3275911;

  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x) / Math.sqrt(2);

  const t = 1.0 / (1.0 + p * x);
  const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

  return 0.5 * (1.0 + sign * y);
}

/**
 * 롤링 윈도우 회귀 분석
 */
export function rollingRegression(x: number[], y: number[], window: number): RegressionResult {
  if (x.length < window || y.length < window) {
    throw new Error(`Insufficient data for rolling regression: need ${window} points`);
  }
  
  // 최근 window 개의 데이터만 사용
  const recentX = x.slice(-window);
  const recentY = y.slice(-window);
  
  return simpleLinearRegression(recentX, recentY);
}

/**
 * Newey-West HAC 표준오차 계산을 위한 최대 지연 계산
 */
export function calculateMaxLags(n: number): number {
  return Math.floor(4 * Math.pow(n / 100, 2 / 9));
}

/**
 * 상관계수 계산
 */
export function calculateCorrelation(x: number[], y: number[]): number {
  if (x.length !== y.length || x.length < 2) {
    return 0;
  }

  const n = x.length;
  const xMean = x.reduce((a, b) => a + b, 0) / n;
  const yMean = y.reduce((a, b) => a + b, 0) / n;
  
  let numerator = 0;
  let xSumSq = 0;
  let ySumSq = 0;
  
  for (let i = 0; i < n; i++) {
    const xDiff = x[i] - xMean;
    const yDiff = y[i] - yMean;
    
    numerator += xDiff * yDiff;
    xSumSq += xDiff * xDiff;
    ySumSq += yDiff * yDiff;
  }
  
  const denominator = Math.sqrt(xSumSq * ySumSq);
  
  return denominator === 0 ? 0 : numerator / denominator;
}
