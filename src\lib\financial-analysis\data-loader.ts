// 주식 데이터 로딩 유틸리티

import { StockData } from './types';

/**
 * Yahoo Finance API를 통해 주식 데이터를 가져옵니다
 */
export async function fetchStockData(symbol: string, years: number = 3): Promise<StockData[]> {
  try {
    // 한국 주식의 경우 .KS 접미사 추가
    const yahooSymbol = symbol.includes('.') ? symbol : `${symbol}.KS`;
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setFullYear(endDate.getFullYear() - years);
    
    const period1 = Math.floor(startDate.getTime() / 1000);
    const period2 = Math.floor(endDate.getTime() / 1000);
    
    const url = `https://query1.finance.yahoo.com/v7/finance/download/${yahooSymbol}?period1=${period1}&period2=${period2}&interval=1d&events=history`;
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const csvText = await response.text();
    return parseCSVData(csvText);
    
  } catch (error) {
    console.error(`Failed to fetch data for ${symbol}:`, error);
    // 폴백 데이터 생성
    return generateFallbackData(symbol, years);
  }
}

/**
 * KOSPI 지수 데이터를 가져옵니다
 */
export async function fetchKOSPIData(years: number = 3): Promise<StockData[]> {
  return fetchStockData('^KS11', years);
}

/**
 * CSV 텍스트를 StockData 배열로 파싱합니다
 */
function parseCSVData(csvText: string): StockData[] {
  const lines = csvText.trim().split('\n');
  const data: StockData[] = [];
  
  // 헤더 라인 스킵
  for (let i = 1; i < lines.length; i++) {
    const columns = lines[i].split(',');
    
    if (columns.length >= 6) {
      const [date, open, high, low, close, , volume] = columns;
      
      // 유효한 숫자 데이터인지 확인
      const openNum = parseFloat(open);
      const highNum = parseFloat(high);
      const lowNum = parseFloat(low);
      const closeNum = parseFloat(close);
      const volumeNum = parseInt(volume) || 0;
      
      if (!isNaN(openNum) && !isNaN(highNum) && !isNaN(lowNum) && !isNaN(closeNum)) {
        data.push({
          date,
          open: openNum,
          high: highNum,
          low: lowNum,
          close: closeNum,
          volume: volumeNum
        });
      }
    }
  }
  
  return data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

/**
 * 데이터 로딩 실패 시 폴백 데이터를 생성합니다
 */
function generateFallbackData(symbol: string, years: number): StockData[] {
  const data: StockData[] = [];
  const endDate = new Date();
  const startDate = new Date();
  startDate.setFullYear(endDate.getFullYear() - years);
  
  let currentDate = new Date(startDate);
  let price = 50000; // 기본 가격
  
  while (currentDate <= endDate) {
    // 주말 제외
    if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
      // 랜덤 워크로 가격 변동 시뮬레이션
      const change = (Math.random() - 0.5) * 0.04; // ±2% 변동
      price *= (1 + change);
      
      const high = price * (1 + Math.random() * 0.02);
      const low = price * (1 - Math.random() * 0.02);
      const open = low + Math.random() * (high - low);
      const close = low + Math.random() * (high - low);
      const volume = Math.floor(Math.random() * 1000000) + 100000;
      
      data.push({
        date: currentDate.toISOString().split('T')[0],
        open,
        high,
        low,
        close,
        volume
      });
    }
    
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return data;
}

/**
 * 수익률 계산
 */
export function calculateReturns(prices: number[]): number[] {
  const returns: number[] = [];
  
  for (let i = 1; i < prices.length; i++) {
    const returnRate = (prices[i] - prices[i - 1]) / prices[i - 1];
    returns.push(returnRate);
  }
  
  return returns;
}

/**
 * 이동평균 계산
 */
export function calculateMovingAverage(values: number[], period: number): number[] {
  const ma: number[] = [];
  
  for (let i = period - 1; i < values.length; i++) {
    const sum = values.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
    ma.push(sum / period);
  }
  
  return ma;
}

/**
 * 표준편차 계산
 */
export function calculateStandardDeviation(values: number[], period: number): number[] {
  const std: number[] = [];
  
  for (let i = period - 1; i < values.length; i++) {
    const slice = values.slice(i - period + 1, i + 1);
    const mean = slice.reduce((a, b) => a + b, 0) / slice.length;
    const variance = slice.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / slice.length;
    std.push(Math.sqrt(variance));
  }
  
  return std;
}
