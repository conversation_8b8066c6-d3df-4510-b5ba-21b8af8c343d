import { NextApiRequest, NextApiResponse } from 'next';
import { runSpeedTrafficAnalysis } from '@/lib/financial-analysis';

// 심볼별 뮤텍스 - 동시 요청 방지
const processing = new Map<string, { active: boolean; startTime: number }>();

// 서킷 브레이커 패턴 - 심볼별 실패 추적
const failureCount = new Map<string, number>();
const lastFailureTime = new Map<string, number>();
const FAILURE_THRESHOLD = 3;
const CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5분

// 오래된 처리 항목 정리 (2분 이상)
const cleanupStaleProcessing = () => {
  const now = Date.now();
  const twoMinutes = 2 * 60 * 1000;
  let cleanedCount = 0;

  for (const [symbol, info] of processing.entries()) {
    if (info.active && (now - info.startTime) > twoMinutes) {
      console.log(`[SPEEDTRAFFIC_API] 오래된 처리 항목 정리: ${symbol} (${now - info.startTime}ms 경과)`);
      processing.delete(symbol);
      cleanedCount++;
    }
  }

  if (cleanedCount > 0) {
    console.log(`[SPEEDTRAFFIC_API] 정리 완료: ${cleanedCount}개 항목 제거`);
  }
};

// 1분마다 정리 작업 실행
setInterval(cleanupStaleProcessing, 60 * 1000);

// 서킷 브레이커 상태 확인
const isCircuitBreakerOpen = (symbol: string): boolean => {
  const failures = failureCount.get(symbol) || 0;
  const lastFailure = lastFailureTime.get(symbol) || 0;
  const now = Date.now();

  if (failures >= FAILURE_THRESHOLD) {
    if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {
      return true; // 서킷 브레이커 열림
    } else {
      // 타임아웃 후 서킷 브레이커 리셋
      failureCount.delete(symbol);
      lastFailureTime.delete(symbol);
      return false;
    }
  }
  return false;
};

// 실패 기록
const recordFailure = (symbol: string) => {
  const failures = (failureCount.get(symbol) || 0) + 1;
  failureCount.set(symbol, failures);
  lastFailureTime.set(symbol, Date.now());
  console.log(`[SPEEDTRAFFIC_API] 실패 기록 ${failures}회: ${symbol}`);
};

// 요약 메시지 생성 함수들
const getMFISummary = (mfi: number): string => {
  if (mfi >= 80) return '과매수 구간 - 매도 신호';
  if (mfi <= 20) return '과매도 구간 - 매수 신호';
  return '중립 구간';
};

const getRSISummary = (rsi: number): string => {
  if (rsi >= 70) return '과매수 구간 - 매도 신호';
  if (rsi <= 30) return '과매도 구간 - 매수 신호';
  return '중립 구간';
};

const getBollingerSummary = (percentB: number): string => {
  if (percentB >= 1) return '상단 밴드 돌파 - 과매수';
  if (percentB <= 0) return '하단 밴드 이탈 - 과매도';
  return '밴드 내부 - 중립';
};

const getCAPMSummary = (beta: number, r2: number): string => {
  if (beta > 1.5 && r2 >= 0.3) return '시장 대비 고위험 고수익';
  if (beta >= 0.8 && beta <= 1.3 && r2 >= 0.3) return '시장과 유사한 움직임';
  return '시장과 낮은 상관관계 또는 방어주';
};

const getGARCHSummary = (var95: number): string => {
  if (var95 > 3) return '단기 고위험 - 주의 필요';
  if (var95 > 2) return '중간 위험 수준';
  return '단기 안정적';
};

const getIndustrySummary = (beta: number, r2: number): string => {
  if (beta > 1.2 && r2 >= 0.5) return '산업 대비 높은 민감도';
  if (beta >= 0.8 && beta <= 1.2 && r2 >= 0.3) return '산업과 비슷한 수준';
  return '산업과 낮은 상관관계';
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { ticker } = req.body;

  if (!ticker) {
    return res.status(400).json({ error: 'Ticker is required' });
  }

  // 서킷 브레이커 확인
  if (isCircuitBreakerOpen(ticker)) {
    console.log(`[SPEEDTRAFFIC_API] 서킷 브레이커 열림: ${ticker}`);
    return res.status(503).json({
      error: 'Service temporarily unavailable',
      message: `Analysis for ${ticker} is temporarily disabled due to repeated failures`,
      ticker,
      retry_after: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)
    });
  }

  // 동시 요청 방지
  const processingInfo = processing.get(ticker);
  if (processingInfo?.active) {
    const elapsed = Date.now() - processingInfo.startTime;
    console.log(`[SPEEDTRAFFIC_API] 이미 처리 중: ${ticker} (${elapsed}ms 경과)`);
    return res.status(429).json({
      error: 'Analysis in progress',
      message: `Analysis for ${ticker} is already in progress`,
      ticker,
      elapsed_ms: elapsed
    });
  }

  // 처리 시작 표시
  processing.set(ticker, { active: true, startTime: Date.now() });

  console.log(`🚀 SpeedTraffic 분석 시작: ${ticker}`);

  try {
    // JavaScript 구현으로 전체 분석 실행
    const analysisResult = await runSpeedTrafficAnalysis(ticker);

    // 결과를 기존 API 형식에 맞게 변환
    const analysisResults = {
      MFI: {
        symbol: analysisResult.mfi.symbol,
        mfi_14: analysisResult.mfi.mfi_14,
        traffic_light: analysisResult.mfi.traffic_light,
        date: analysisResult.mfi.date,
        summary_ko: `MFI 지수: ${analysisResult.mfi.mfi_14}% - ${getMFISummary(analysisResult.mfi.mfi_14)}`
      },
      RSI: {
        symbol: analysisResult.rsi.symbol,
        rsi_14: analysisResult.rsi.rsi_14,
        traffic_light: analysisResult.rsi.traffic_light,
        date: analysisResult.rsi.date,
        summary_ko: `RSI 지수: ${analysisResult.rsi.rsi_14}% - ${getRSISummary(analysisResult.rsi.rsi_14)}`
      },
      Bollinger: {
        symbol: analysisResult.bollinger.symbol,
        percent_b: analysisResult.bollinger.percent_b,
        traffic_light: analysisResult.bollinger.traffic_light,
        date: analysisResult.bollinger.date,
        summary_ko: `%B 지수: ${analysisResult.bollinger.percent_b} - ${getBollingerSummary(analysisResult.bollinger.percent_b)}`
      },
      CAPM: {
        symbol: analysisResult.capm.symbol,
        beta_market: analysisResult.capm.beta_market,
        r2_market: analysisResult.capm.r2_market,
        tstat_market: analysisResult.capm.tstat_market,
        traffic_light: analysisResult.capm.traffic_light,
        date: analysisResult.capm.date,
        summary_ko: `시장 베타: ${analysisResult.capm.beta_market} (R²: ${analysisResult.capm.r2_market}) - ${getCAPMSummary(analysisResult.capm.beta_market, analysisResult.capm.r2_market)}`
      },
      GARCH: {
        symbol: analysisResult.garch.symbol,
        sigma_pct: analysisResult.garch.sigma_pct,
        var95_pct: analysisResult.garch.var95_pct,
        var99_pct: analysisResult.garch.var99_pct,
        traffic_light: analysisResult.garch.traffic_light,
        date: analysisResult.garch.date,
        summary_ko: `일일 변동성: ${analysisResult.garch.sigma_pct}%, VaR(95%): ${analysisResult.garch.var95_pct}% - ${getGARCHSummary(analysisResult.garch.var95_pct)}`
      },
      Industry: {
        symbol: analysisResult.industry.symbol,
        industry: analysisResult.industry.industry,
        beta: analysisResult.industry.beta,
        r2: analysisResult.industry.r2,
        tstat: analysisResult.industry.tstat,
        traffic_light: analysisResult.industry.traffic_light,
        date: analysisResult.industry.date,
        summary_ko: `산업 베타: ${analysisResult.industry.beta} (${analysisResult.industry.industry}) - ${getIndustrySummary(analysisResult.industry.beta, analysisResult.industry.r2)}`
      }
    };

    console.log(`✅ SpeedTraffic 분석 완료: ${ticker}`);

    // 처리 완료 표시
    processing.delete(ticker);

    res.status(200).json({
      ticker,
      timestamp: new Date().toISOString(),
      analysis: analysisResults,
      traffic_lights: analysisResult.traffic_lights,
      implementation: 'javascript' // JavaScript 구현임을 표시
    });

  } catch (error) {
    console.error(`❌ SpeedTraffic 분석 실패 (${ticker}):`, error);
    
    // 실패 기록
    recordFailure(ticker);
    
    // 처리 완료 표시
    processing.delete(ticker);

    res.status(500).json({
      error: 'Analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      ticker
    });
  }
}
