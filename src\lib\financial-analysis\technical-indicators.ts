// 기술적 지표 계산 함수들

import { StockData, MFIResult, RSIResult, BollingerResult } from './types';
import { calculateMovingAverage, calculateStandardDeviation } from './data-loader';

/**
 * Money Flow Index (MFI) 계산
 */
export function calculateMFI(data: StockData[], period: number = 14): MFIResult {
  if (data.length < period) {
    throw new Error(`Insufficient data for MFI calculation: ${data.length} days`);
  }

  // Typical Price 계산
  const typicalPrices = data.map(d => (d.high + d.low + d.close) / 3);
  
  // Raw Money Flow 계산
  const rawMoneyFlow = data.map((d, i) => typicalPrices[i] * d.volume);
  
  // Positive/Negative Money Flow 계산
  const positiveFlow: number[] = [];
  const negativeFlow: number[] = [];
  
  for (let i = 1; i < data.length; i++) {
    if (typicalPrices[i] > typicalPrices[i - 1]) {
      positiveFlow.push(rawMoneyFlow[i]);
      negativeFlow.push(0);
    } else if (typicalPrices[i] < typicalPrices[i - 1]) {
      positiveFlow.push(0);
      negativeFlow.push(rawMoneyFlow[i]);
    } else {
      positiveFlow.push(0);
      negativeFlow.push(0);
    }
  }
  
  // MFI 계산
  const mfiValues: number[] = [];
  
  for (let i = period - 1; i < positiveFlow.length; i++) {
    const posSum = positiveFlow.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
    const negSum = negativeFlow.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
    
    if (negSum === 0) {
      mfiValues.push(100);
    } else {
      const mfr = posSum / negSum;
      const mfi = 100 - (100 / (1 + mfr));
      mfiValues.push(mfi);
    }
  }
  
  const latestMFI = mfiValues[mfiValues.length - 1];
  const latestDate = data[data.length - 1].date;
  
  // 신호등 결정
  let trafficLight: 'red' | 'yellow' | 'green';
  if (latestMFI >= 80) {
    trafficLight = 'red';    // 과매수
  } else if (latestMFI <= 20) {
    trafficLight = 'green';  // 과매도
  } else {
    trafficLight = 'yellow'; // 중립
  }
  
  return {
    symbol: '', // 호출하는 곳에서 설정
    date: latestDate,
    mfi_14: Math.round(latestMFI * 100) / 100,
    traffic_light: trafficLight
  };
}

/**
 * Relative Strength Index (RSI) 계산
 */
export function calculateRSI(data: StockData[], period: number = 14): RSIResult {
  if (data.length < period + 1) {
    throw new Error(`Insufficient data for RSI calculation: ${data.length} days`);
  }

  const prices = data.map(d => d.close);
  const gains: number[] = [];
  const losses: number[] = [];
  
  // 가격 변화 계산
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? -change : 0);
  }
  
  // 지수이동평균 방식으로 RSI 계산
  const alpha = 1 / period;
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
  
  const rsiValues: number[] = [];
  
  for (let i = period; i < gains.length; i++) {
    avgGain = (1 - alpha) * avgGain + alpha * gains[i];
    avgLoss = (1 - alpha) * avgLoss + alpha * losses[i];
    
    const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    rsiValues.push(rsi);
  }
  
  const latestRSI = rsiValues[rsiValues.length - 1];
  const latestDate = data[data.length - 1].date;
  
  // 신호등 결정
  let trafficLight: 'red' | 'yellow' | 'green';
  if (latestRSI >= 70) {
    trafficLight = 'red';    // 과매수
  } else if (latestRSI <= 30) {
    trafficLight = 'green';  // 과매도
  } else {
    trafficLight = 'yellow'; // 중립
  }
  
  return {
    symbol: '', // 호출하는 곳에서 설정
    date: latestDate,
    rsi_14: Math.round(latestRSI * 100) / 100,
    traffic_light: trafficLight
  };
}

/**
 * Bollinger Bands 계산
 */
export function calculateBollinger(data: StockData[], period: number = 20, k: number = 2.0): BollingerResult {
  if (data.length < period) {
    throw new Error(`Insufficient data for Bollinger calculation: ${data.length} days`);
  }

  const prices = data.map(d => d.close);
  
  // 이동평균과 표준편차 계산
  const sma = calculateMovingAverage(prices, period);
  const std = calculateStandardDeviation(prices, period);
  
  // 볼린저 밴드 계산
  const upperBand = sma.map((ma, i) => ma + k * std[i]);
  const lowerBand = sma.map((ma, i) => ma - k * std[i]);
  
  // %B 계산 (현재 가격의 밴드 내 위치)
  const currentPrice = prices[prices.length - 1];
  const currentUpper = upperBand[upperBand.length - 1];
  const currentLower = lowerBand[lowerBand.length - 1];
  
  const percentB = (currentPrice - currentLower) / (currentUpper - currentLower);
  const latestDate = data[data.length - 1].date;
  
  // 신호등 결정
  let trafficLight: 'red' | 'yellow' | 'green';
  if (percentB >= 1) {
    trafficLight = 'red';    // 상단 밴드 돌파, 과매수
  } else if (percentB <= 0) {
    trafficLight = 'green';  // 하단 밴드 이탈, 과매도
  } else {
    trafficLight = 'yellow'; // 밴드 내부, 중립
  }
  
  return {
    symbol: '', // 호출하는 곳에서 설정
    date: latestDate,
    percent_b: Math.round(percentB * 100) / 100,
    traffic_light: trafficLight
  };
}
