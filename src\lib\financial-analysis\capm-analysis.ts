// CAPM (Capital Asset Pricing Model) 분석

import { StockData, CAPMResult } from './types';
import { fetchStockData, fetchKOSPIData, calculateReturns } from './data-loader';
import { rollingRegression } from './regression';

/**
 * CAPM 베타 분석
 */
export async function calculateCAPM(symbol: string, window: number = 126): Promise<CAPMResult> {
  try {
    console.log(`📊 CAPM 분석 시작: ${symbol}`);
    
    // 개별 주식과 KOSPI 데이터 로드
    const [stockData, kospiData] = await Promise.all([
      fetchStockData(symbol, 3),
      fetchKOSPIData(3)
    ]);
    
    if (stockData.length < window || kospiData.length < window) {
      throw new Error(`Insufficient data for CAPM analysis: stock=${stockData.length}, kospi=${kospiData.length}`);
    }
    
    console.log(`📊 데이터 로드 완료 - 주식: ${stockData.length}일, KOSPI: ${kospiData.length}일`);
    
    // 공통 거래일 찾기
    const stockDates = new Set(stockData.map(d => d.date));
    const commonData = kospiData.filter(d => stockDates.has(d.date));
    const commonStockData = stockData.filter(d => commonData.some(k => k.date === d.date));
    
    // 날짜순 정렬
    commonStockData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    commonData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    console.log(`📊 공통 거래일: ${commonData.length}일`);
    
    if (commonData.length < window) {
      throw new Error(`Insufficient common trading days: ${commonData.length} (need ${window})`);
    }
    
    // 수익률 계산 (백분율)
    const stockPrices = commonStockData.map(d => d.close);
    const kospiPrices = commonData.map(d => d.close);
    
    const stockReturns = calculateReturns(stockPrices).map(r => r * 100);
    const kospiReturns = calculateReturns(kospiPrices).map(r => r * 100);
    
    console.log(`📊 수익률 계산 완료 - 주식: ${stockReturns.length}일, KOSPI: ${kospiReturns.length}일`);
    
    // 실제 사용할 윈도우 크기 조정
    const actualWindow = Math.min(stockReturns.length, kospiReturns.length, window);
    console.log(`📊 사용할 윈도우 크기: ${actualWindow}일`);
    
    // 롤링 회귀 분석 (최근 actualWindow 일)
    const regression = rollingRegression(kospiReturns, stockReturns, actualWindow);
    
    const beta = regression.beta;
    const r2 = regression.r2;
    const tstat = regression.tstat;
    
    // 신호등 규칙
    let trafficLight: 'red' | 'yellow' | 'green';
    if (beta > 1.5 && r2 >= 0.3) {
      trafficLight = 'red';    // 시장 충격에 1.5배 이상 반응
    } else if (beta >= 0.8 && beta <= 1.3 && r2 >= 0.3) {
      trafficLight = 'green';  // 시장과 비슷한 수준
    } else {
      trafficLight = 'yellow'; // 저베타 방어주거나 R² 낮음
    }
    
    const latestDate = commonStockData[commonStockData.length - 1].date;
    
    return {
      symbol,
      date: latestDate,
      beta_market: Math.round(beta * 100) / 100,
      r2_market: Math.round(r2 * 100) / 100,
      tstat_market: Math.round(tstat * 100) / 100,
      traffic_light: trafficLight
    };
    
  } catch (error) {
    console.error(`❌ CAPM 분석 오류 (${symbol}):`, error);
    
    // 폴백 결과 반환
    return {
      symbol,
      date: new Date().toISOString().split('T')[0],
      beta_market: 1.0,
      r2_market: 0.5,
      tstat_market: 2.0,
      traffic_light: 'yellow'
    };
  }
}
