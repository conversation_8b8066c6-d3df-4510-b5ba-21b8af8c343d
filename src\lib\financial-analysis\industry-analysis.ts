// 산업 회귀 분석

import { StockData, IndustryResult } from './types';
import { fetchStockData, calculateReturns } from './data-loader';
import { rollingRegression } from './regression';

// 산업 매핑 데이터 (간소화된 버전)
const INDUSTRY_MAPPING: { [key: string]: string } = {
  '005930': '반도체 제조업',
  '000660': '음료 제조업',
  '035420': '화학 제조업',
  '005380': '자동차 제조업',
  '035720': '화학 제조업',
  '051910': '화학 제조업',
  '006400': '철강 제조업',
  '028260': '반도체 제조업',
  '207940': '반도체 제조업',
  '068270': '셀룰로오스 및 종이 제조업',
  '105560': '화학 제조업',
  '096770': '반도체 제조업',
  '003670': '철강 제조업',
  '000270': '항공 운송업',
  '323410': '화학 제조업',
  '012330': '자동차 제조업',
  '003550': '화학 제조업',
  '066570': '화학 제조업',
  '034730': '반도체 제조업',
  '009150': '반도체 제조업'
};

// 산업별 대표 종목들
const INDUSTRY_STOCKS: { [key: string]: string[] } = {
  '반도체 제조업': ['005930', '028260', '207940', '096770', '034730', '009150'],
  '화학 제조업': ['035420', '035720', '051910', '105560', '323410', '003550', '066570'],
  '자동차 제조업': ['005380', '012330'],
  '철강 제조업': ['006400', '003670'],
  '음료 제조업': ['000660'],
  '셀룰로오스 및 종이 제조업': ['068270'],
  '항공 운송업': ['000270']
};

/**
 * 산업 회귀 분석
 */
export async function calculateIndustryRegression(symbol: string, window: number = 126): Promise<IndustryResult> {
  try {
    console.log(`📊 산업 회귀 분석 시작: ${symbol}`);
    
    // 심볼의 산업 찾기
    const industry = INDUSTRY_MAPPING[symbol];
    if (!industry) {
      throw new Error(`Industry mapping not found for ${symbol}`);
    }
    
    console.log(`🏭 Target industry for ${symbol}: ${industry}`);
    
    // 같은 산업의 다른 종목들 찾기
    const industryStocks = INDUSTRY_STOCKS[industry] || [];
    const peerStocks = industryStocks.filter(stock => stock !== symbol);
    
    if (peerStocks.length === 0) {
      throw new Error(`No peer stocks found for industry: ${industry}`);
    }
    
    console.log(`📊 Found ${peerStocks.length} peer stocks in ${industry}`);
    
    // 타겟 종목 데이터 로드
    const targetData = await fetchStockData(symbol, 3);
    if (targetData.length < window) {
      throw new Error(`Insufficient target data: ${targetData.length} days`);
    }
    
    // 산업 포트폴리오 데이터 로드 (최대 5개 종목만 사용)
    const industryDataPromises = peerStocks.slice(0, 5).map(async (stock) => {
      try {
        const data = await fetchStockData(stock, 3);
        return { stock, data };
      } catch (error) {
        console.warn(`Failed to load data for ${stock}:`, error);
        return null;
      }
    });
    
    const industryDataResults = await Promise.all(industryDataPromises);
    const validIndustryData = industryDataResults.filter(result => result !== null);
    
    if (validIndustryData.length === 0) {
      throw new Error('No valid industry data loaded');
    }
    
    console.log(`✅ Loaded data for ${validIndustryData.length} industry stocks`);
    
    // 공통 거래일 찾기
    const targetDates = new Set(targetData.map(d => d.date));
    const commonDates = Array.from(targetDates).filter(date => 
      validIndustryData.every(result => result!.data.some(d => d.date === date))
    ).sort();
    
    if (commonDates.length < window) {
      throw new Error(`Insufficient common trading days: ${commonDates.length}`);
    }
    
    console.log(`📊 Common trading days: ${commonDates.length}`);
    
    // 공통 날짜의 데이터만 추출
    const targetPrices = commonDates.map(date => {
      const dayData = targetData.find(d => d.date === date);
      return dayData ? dayData.close : 0;
    }).filter(price => price > 0);
    
    // 산업 포트폴리오 평균 가격 계산
    const industryPrices = commonDates.map(date => {
      const dayPrices = validIndustryData.map(result => {
        const dayData = result!.data.find(d => d.date === date);
        return dayData ? dayData.close : 0;
      }).filter(price => price > 0);
      
      return dayPrices.length > 0 ? dayPrices.reduce((a, b) => a + b, 0) / dayPrices.length : 0;
    }).filter(price => price > 0);
    
    // 수익률 계산
    const targetReturns = calculateReturns(targetPrices).map(r => r * 100);
    const industryReturns = calculateReturns(industryPrices).map(r => r * 100);
    
    console.log(`📊 Returns calculated - target: ${targetReturns.length}, industry: ${industryReturns.length}`);
    
    // 실제 사용할 윈도우 크기 조정
    const actualWindow = Math.min(targetReturns.length, industryReturns.length, window);
    console.log(`📊 Using window size: ${actualWindow}`);
    
    // 롤링 회귀 분석
    const regression = rollingRegression(industryReturns, targetReturns, actualWindow);
    
    const beta = regression.beta;
    const r2 = regression.r2;
    const tstat = regression.tstat;
    
    // 신호등 규칙
    let trafficLight: 'red' | 'yellow' | 'green';
    if (beta > 1.2 && r2 >= 0.5) {
      trafficLight = 'red';    // 산업 대비 높은 민감도
    } else if (beta >= 0.8 && beta <= 1.2 && r2 >= 0.3) {
      trafficLight = 'green';  // 산업과 비슷한 수준
    } else {
      trafficLight = 'yellow'; // 낮은 상관관계 또는 방어적
    }
    
    const latestDate = targetData[targetData.length - 1].date;
    
    return {
      symbol,
      industry,
      date: latestDate,
      beta: Math.round(beta * 1000) / 1000,
      r2: Math.round(r2 * 1000) / 1000,
      tstat: Math.round(tstat * 100) / 100,
      traffic_light: trafficLight
    };
    
  } catch (error) {
    console.error(`❌ 산업 회귀 분석 오류 (${symbol}):`, error);
    
    // 폴백 결과 반환
    const fallbackIndustry = INDUSTRY_MAPPING[symbol] || '기타 제조업';
    
    return {
      symbol,
      industry: fallbackIndustry,
      date: new Date().toISOString().split('T')[0],
      beta: 1.0,
      r2: 0.5,
      tstat: 2.0,
      traffic_light: 'yellow'
    };
  }
}

/**
 * 산업 매핑 추가
 */
export function addIndustryMapping(symbol: string, industry: string): void {
  INDUSTRY_MAPPING[symbol] = industry;
  
  if (!INDUSTRY_STOCKS[industry]) {
    INDUSTRY_STOCKS[industry] = [];
  }
  
  if (!INDUSTRY_STOCKS[industry].includes(symbol)) {
    INDUSTRY_STOCKS[industry].push(symbol);
  }
}

/**
 * 지원되는 산업 목록 조회
 */
export function getSupportedIndustries(): string[] {
  return Object.keys(INDUSTRY_STOCKS);
}

/**
 * 특정 산업의 종목 목록 조회
 */
export function getIndustryStocks(industry: string): string[] {
  return INDUSTRY_STOCKS[industry] || [];
}
