import { test, expect } from '@playwright/test';

test.describe('SpeedTraffic 기능 테스트', () => {
  test.beforeEach(async ({ page }) => {
    // 개발 서버가 실행 중이라고 가정
    await page.goto('http://localhost:3001');
  });

  test('메인 페이지에서 SpeedTraffic 페이지로 이동', async ({ page }) => {
    // 메인 페이지에서 SpeedTraffic 링크 찾기
    const speedTrafficLink = page.locator('a[href="/speedtraffic"]').first();
    await expect(speedTrafficLink).toBeVisible();
    
    // SpeedTraffic 페이지로 이동
    await speedTrafficLink.click();
    
    // URL 확인
    await expect(page).toHaveURL('/speedtraffic');
    
    // 페이지 제목 확인
    await expect(page.locator('h1')).toContainText('SpeedTraffic');
  });

  test('SpeedTraffic 분석 실행 테스트', async ({ page }) => {
    // SpeedTraffic 페이지로 직접 이동
    await page.goto('http://localhost:3001/speedtraffic');
    
    // 페이지 로딩 대기
    await page.waitForLoadState('networkidle');
    
    // 종목 입력 필드 찾기
    const tickerInput = page.locator('input[placeholder*="종목"], input[placeholder*="ticker"], input[type="text"]').first();
    await expect(tickerInput).toBeVisible();
    
    // 테스트 종목 입력 (삼성전자)
    await tickerInput.fill('005930');
    
    // 분석 시작 버튼 찾기 및 클릭
    const analyzeButton = page.locator('button').filter({ hasText: /분석|시작|실행|SpeedTraffic/ }).first();
    await expect(analyzeButton).toBeVisible();
    await analyzeButton.click();
    
    // 로딩 상태 확인
    const loadingIndicator = page.locator('[data-testid="loading"], .loading, .spinner').first();
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toBeVisible();
    }
    
    // 분석 완료 대기 (최대 60초)
    await page.waitForFunction(() => {
      const loadingElements = document.querySelectorAll('[data-testid="loading"], .loading, .spinner');
      return loadingElements.length === 0 || Array.from(loadingElements).every(el => !el.offsetParent);
    }, { timeout: 60000 });
    
    // 신호등 결과 확인
    const trafficLights = page.locator('[data-testid="traffic-light"], .traffic-light').first();
    await expect(trafficLights).toBeVisible({ timeout: 10000 });
    
    // 분석 결과 섹션들 확인
    const resultSections = [
      'MFI', 'RSI', 'Bollinger', 'CAPM', 'GARCH', 'Industry'
    ];
    
    for (const section of resultSections) {
      const sectionElement = page.locator(`[data-testid="${section}"], .${section.toLowerCase()}`).first();
      if (await sectionElement.isVisible()) {
        await expect(sectionElement).toBeVisible();
        console.log(`✅ ${section} 섹션 확인됨`);
      } else {
        console.log(`⚠️ ${section} 섹션을 찾을 수 없음`);
      }
    }
  });

  test('API 엔드포인트 직접 테스트', async ({ request }) => {
    // SpeedTraffic API 직접 호출
    const response = await request.post('http://localhost:3001/api/speedtraffic_analysis', {
      data: {
        ticker: '005930'
      },
      timeout: 60000 // 60초 타임아웃
    });
    
    // 응답 상태 확인
    expect(response.status()).toBe(200);
    
    // 응답 데이터 확인
    const responseData = await response.json();
    
    // 기본 구조 확인
    expect(responseData).toHaveProperty('ticker', '005930');
    expect(responseData).toHaveProperty('timestamp');
    expect(responseData).toHaveProperty('analysis');
    expect(responseData).toHaveProperty('traffic_lights');
    expect(responseData).toHaveProperty('implementation', 'javascript');
    
    // 분석 결과 확인
    const analysis = responseData.analysis;
    expect(analysis).toHaveProperty('MFI');
    expect(analysis).toHaveProperty('RSI');
    expect(analysis).toHaveProperty('Bollinger');
    expect(analysis).toHaveProperty('CAPM');
    expect(analysis).toHaveProperty('GARCH');
    expect(analysis).toHaveProperty('Industry');
    
    // MFI 결과 구조 확인
    expect(analysis.MFI).toHaveProperty('symbol', '005930');
    expect(analysis.MFI).toHaveProperty('mfi_14');
    expect(analysis.MFI).toHaveProperty('traffic_light');
    expect(analysis.MFI.traffic_light).toMatch(/^(red|yellow|green)$/);
    
    // RSI 결과 구조 확인
    expect(analysis.RSI).toHaveProperty('symbol', '005930');
    expect(analysis.RSI).toHaveProperty('rsi_14');
    expect(analysis.RSI).toHaveProperty('traffic_light');
    expect(analysis.RSI.traffic_light).toMatch(/^(red|yellow|green)$/);
    
    // Bollinger 결과 구조 확인
    expect(analysis.Bollinger).toHaveProperty('symbol', '005930');
    expect(analysis.Bollinger).toHaveProperty('percent_b');
    expect(analysis.Bollinger).toHaveProperty('traffic_light');
    expect(analysis.Bollinger.traffic_light).toMatch(/^(red|yellow|green)$/);
    
    // CAPM 결과 구조 확인
    expect(analysis.CAPM).toHaveProperty('symbol', '005930');
    expect(analysis.CAPM).toHaveProperty('beta_market');
    expect(analysis.CAPM).toHaveProperty('r2_market');
    expect(analysis.CAPM).toHaveProperty('traffic_light');
    expect(analysis.CAPM.traffic_light).toMatch(/^(red|yellow|green)$/);
    
    // GARCH 결과 구조 확인
    expect(analysis.GARCH).toHaveProperty('symbol', '005930');
    expect(analysis.GARCH).toHaveProperty('sigma_pct');
    expect(analysis.GARCH).toHaveProperty('var95_pct');
    expect(analysis.GARCH).toHaveProperty('var99_pct');
    expect(analysis.GARCH).toHaveProperty('traffic_light');
    expect(analysis.GARCH.traffic_light).toMatch(/^(red|yellow|green)$/);
    
    // Industry 결과 구조 확인
    expect(analysis.Industry).toHaveProperty('symbol', '005930');
    expect(analysis.Industry).toHaveProperty('industry');
    expect(analysis.Industry).toHaveProperty('beta');
    expect(analysis.Industry).toHaveProperty('r2');
    expect(analysis.Industry).toHaveProperty('traffic_light');
    expect(analysis.Industry.traffic_light).toMatch(/^(red|yellow|green)$/);
    
    // 신호등 상태 확인
    const trafficLights = responseData.traffic_lights;
    expect(trafficLights).toHaveProperty('technical');
    expect(trafficLights).toHaveProperty('industry');
    expect(trafficLights).toHaveProperty('market');
    expect(trafficLights).toHaveProperty('risk');
    
    expect(trafficLights.technical).toMatch(/^(red|yellow|green)$/);
    expect(trafficLights.industry).toMatch(/^(red|yellow|green)$/);
    expect(trafficLights.market).toMatch(/^(red|yellow|green)$/);
    expect(trafficLights.risk).toMatch(/^(red|yellow|green)$/);
    
    console.log('✅ API 테스트 완료:', {
      ticker: responseData.ticker,
      implementation: responseData.implementation,
      trafficLights: responseData.traffic_lights,
      mfi: analysis.MFI.mfi_14,
      rsi: analysis.RSI.rsi_14,
      beta: analysis.CAPM.beta_market,
      industry: analysis.Industry.industry
    });
  });

  test('여러 종목 연속 테스트', async ({ request }) => {
    const testTickers = ['005930', '000660', '035420']; // 삼성전자, SK하이닉스, NAVER
    
    for (const ticker of testTickers) {
      console.log(`🧪 ${ticker} 테스트 시작`);
      
      const response = await request.post('http://localhost:3001/api/speedtraffic_analysis', {
        data: { ticker },
        timeout: 60000
      });
      
      expect(response.status()).toBe(200);
      
      const responseData = await response.json();
      expect(responseData.ticker).toBe(ticker);
      expect(responseData.implementation).toBe('javascript');
      
      console.log(`✅ ${ticker} 테스트 완료`);
      
      // 다음 요청 전 잠시 대기 (API 부하 방지)
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  });

  test('오류 처리 테스트', async ({ request }) => {
    // 잘못된 종목 코드로 테스트
    const response = await request.post('http://localhost:3001/api/speedtraffic_analysis', {
      data: {
        ticker: 'INVALID_TICKER'
      },
      timeout: 60000
    });
    
    // 오류 응답이거나 폴백 데이터 응답이어야 함
    if (response.status() === 500) {
      const errorData = await response.json();
      expect(errorData).toHaveProperty('error');
      expect(errorData).toHaveProperty('ticker', 'INVALID_TICKER');
      console.log('✅ 오류 처리 확인됨');
    } else if (response.status() === 200) {
      const responseData = await response.json();
      expect(responseData.ticker).toBe('INVALID_TICKER');
      console.log('✅ 폴백 데이터 처리 확인됨');
    }
  });
});
