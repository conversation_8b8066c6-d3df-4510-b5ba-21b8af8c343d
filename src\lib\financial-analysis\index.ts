// 금융 분석 라이브러리 메인 엔트리 포인트

export * from './types';
export * from './data-loader';
export * from './technical-indicators';
export * from './regression';
export * from './capm-analysis';
export * from './garch-analysis';
export * from './industry-analysis';

import { 
  MFIResult, 
  RSIResult, 
  BollingerResult, 
  CAPMResult, 
  GARCHResult, 
  IndustryResult 
} from './types';
import { fetchStockData } from './data-loader';
import { calculateMFI, calculateRSI, calculateBollinger } from './technical-indicators';
import { calculateCAPM } from './capm-analysis';
import { calculateGARCH } from './garch-analysis';
import { calculateIndustryRegression } from './industry-analysis';

/**
 * 전체 SpeedTraffic 분석 실행
 */
export async function runSpeedTrafficAnalysis(symbol: string): Promise<{
  mfi: MFIResult;
  rsi: RSIResult;
  bollinger: BollingerResult;
  capm: CAPMResult;
  garch: GARCHResult;
  industry: IndustryResult;
  traffic_lights: {
    technical: string;
    industry: string;
    market: string;
    risk: string;
  };
}> {
  console.log(`🚀 SpeedTraffic 분석 시작: ${symbol}`);
  
  try {
    // 기본 주식 데이터 로드
    const stockData = await fetchStockData(symbol, 3);
    
    if (stockData.length < 20) {
      throw new Error(`Insufficient data for analysis: ${stockData.length} days`);
    }
    
    // 기술적 지표 계산
    console.log(`📊 기술적 지표 계산 중...`);
    const mfi = calculateMFI(stockData);
    mfi.symbol = symbol;
    
    const rsi = calculateRSI(stockData);
    rsi.symbol = symbol;
    
    const bollinger = calculateBollinger(stockData);
    bollinger.symbol = symbol;
    
    // 기술적 분석 신호등 결정 (MFI, RSI, Bollinger 종합)
    const technicalLights = [mfi.traffic_light, rsi.traffic_light, bollinger.traffic_light];
    const redCount = technicalLights.filter(light => light === 'red').length;
    const greenCount = technicalLights.filter(light => light === 'green').length;
    
    let technicalLight: string;
    if (redCount >= 2) {
      technicalLight = 'red';
    } else if (greenCount >= 2) {
      technicalLight = 'green';
    } else {
      technicalLight = 'yellow';
    }
    
    // 고급 분석 (병렬 실행)
    console.log(`📊 고급 분석 실행 중...`);
    const [capm, garch, industry] = await Promise.all([
      calculateCAPM(symbol).catch(error => {
        console.warn(`CAPM 분석 실패: ${error.message}`);
        return {
          symbol,
          date: new Date().toISOString().split('T')[0],
          beta_market: 1.0,
          r2_market: 0.5,
          tstat_market: 2.0,
          traffic_light: 'yellow' as const
        };
      }),
      calculateGARCH(symbol).catch(error => {
        console.warn(`GARCH 분석 실패: ${error.message}`);
        return {
          symbol,
          date: new Date().toISOString().split('T')[0],
          sigma_pct: 2.0,
          var95_pct: 3.3,
          var99_pct: 4.7,
          traffic_light: 'yellow' as const
        };
      }),
      calculateIndustryRegression(symbol).catch(error => {
        console.warn(`산업 분석 실패: ${error.message}`);
        return {
          symbol,
          industry: '기타 제조업',
          date: new Date().toISOString().split('T')[0],
          beta: 1.0,
          r2: 0.5,
          tstat: 2.0,
          traffic_light: 'yellow' as const
        };
      })
    ]);
    
    console.log(`✅ SpeedTraffic 분석 완료: ${symbol}`);
    
    return {
      mfi,
      rsi,
      bollinger,
      capm,
      garch,
      industry,
      traffic_lights: {
        technical: technicalLight,
        industry: industry.traffic_light,
        market: capm.traffic_light,
        risk: garch.traffic_light
      }
    };
    
  } catch (error) {
    console.error(`❌ SpeedTraffic 분석 실패 (${symbol}):`, error);
    throw error;
  }
}
