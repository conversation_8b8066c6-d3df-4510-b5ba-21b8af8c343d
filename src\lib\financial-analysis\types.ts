// 금융 분석 라이브러리 타입 정의

export interface StockData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface AnalysisResult {
  symbol: string;
  date: string;
  traffic_light: 'red' | 'yellow' | 'green';
}

export interface MFIResult extends AnalysisResult {
  mfi_14: number;
}

export interface RSIResult extends AnalysisResult {
  rsi_14: number;
}

export interface BollingerResult extends AnalysisResult {
  percent_b: number;
}

export interface CAPMResult extends AnalysisResult {
  beta_market: number;
  r2_market: number;
  tstat_market: number;
}

export interface GARCHResult extends AnalysisResult {
  sigma_pct: number;
  var95_pct: number;
  var99_pct: number;
}

export interface IndustryResult extends AnalysisResult {
  industry: string;
  beta: number;
  r2: number;
  tstat: number;
}

export interface MarketData {
  [symbol: string]: StockData[];
}

export interface RegressionResult {
  beta: number;
  r2: number;
  tstat: number;
  pvalue: number;
}
